"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../../prisma/prisma.service");
const supabase_service_1 = require("../../supabase/supabase.service");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService, prismaService, supabaseService) {
        const jwtSecret = configService.get('SUPABASE_JWT_SECRET') || configService.get('JWT_SECRET');
        if (!jwtSecret) {
            throw new Error('JWT secret is required. Please set SUPABASE_JWT_SECRET or JWT_SECRET in your environment variables.');
        }
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: true,
            secretOrKey: jwtSecret,
            passReqToCallback: true,
        });
        this.configService = configService;
        this.prismaService = prismaService;
        this.supabaseService = supabaseService;
    }
    async validate(req, payload) {
        console.log('--- JWT STRATEGY VALIDATE METHOD ENTERED ---');
        try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                throw new common_1.UnauthorizedException('Missing or invalid authorization header.');
            }
            const jwtToken = authHeader.substring(7);
            console.log('JWT Token extracted from header');
            const supabase = this.supabaseService.getAdminClient();
            const { data: { user }, error } = await supabase.auth.getUser(jwtToken);
            if (error) {
                console.error('[JwtStrategy] Supabase JWT validation error:', error);
                throw new common_1.UnauthorizedException('Invalid JWT token: ' + error.message);
            }
            if (!user) {
                console.error('[JwtStrategy] No user returned from Supabase JWT validation');
                throw new common_1.UnauthorizedException('Invalid JWT token: no user found');
            }
            console.log(`[JwtStrategy] Supabase JWT validation successful for user: ${user.id}`);
            console.log(`[JwtStrategy] Attempting to find user in database...`);
            const userProfile = await this.prismaService.user.findUnique({
                where: { authUserId: user.id },
            });
            if (userProfile) {
                console.log(`[JwtStrategy] User profile found in DB for authUserId: ${user.id}, public.users ID: ${userProfile.id}`);
            }
            else {
                console.log(`[JwtStrategy] User profile NOT found in DB for authUserId: ${user.id}. This is okay for initial sync.`);
            }
            console.log('--- JWT STRATEGY VALIDATE METHOD COMPLETED SUCCESSFULLY ---');
            return {
                authData: user,
                dbProfile: userProfile,
                profileExistsInDb: !!userProfile,
            };
        }
        catch (error) {
            console.error('--- JWT STRATEGY VALIDATE METHOD ERROR ---');
            console.error('Error details:', error);
            console.error('Error stack:', error.stack);
            throw error;
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService,
        supabase_service_1.SupabaseService])
], JwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map