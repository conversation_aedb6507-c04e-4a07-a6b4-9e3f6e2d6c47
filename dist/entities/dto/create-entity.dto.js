"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEntityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const prisma_1 = require("../../../generated/prisma/index.js");
const create_tool_details_dto_1 = require("./details/create-tool-details.dto");
const create_course_details_dto_1 = require("./details/create-course-details.dto");
const create_agency_details_dto_1 = require("./details/create-agency-details.dto");
const create_content_creator_details_dto_1 = require("./details/create-content-creator-details.dto");
const create_community_details_dto_1 = require("./details/create-community-details.dto");
const create_newsletter_details_dto_1 = require("./details/create-newsletter-details.dto");
const create_dataset_details_dto_1 = require("./details/create-dataset-details.dto");
const create_research_paper_details_dto_1 = require("./details/create-research-paper-details.dto");
const create_software_details_dto_1 = require("./details/create-software-details.dto");
const create_model_details_dto_1 = require("./details/create-model-details.dto");
const create_project_reference_details_dto_1 = require("./details/create-project-reference-details.dto");
const create_service_provider_details_dto_1 = require("./details/create-service-provider-details.dto");
const create_investor_details_dto_1 = require("./details/create-investor-details.dto");
const create_event_details_dto_1 = require("./details/create-event-details.dto");
const create_job_details_dto_1 = require("./details/create-job-details.dto");
const create_grant_details_dto_1 = require("./details/create-grant-details.dto");
const create_bounty_details_dto_1 = require("./details/create-bounty-details.dto");
const create_hardware_details_dto_1 = require("./details/create-hardware-details.dto");
const create_news_details_dto_1 = require("./details/create-news-details.dto");
const create_book_details_dto_1 = require("./details/create-book-details.dto");
const create_podcast_details_dto_1 = require("./details/create-podcast-details.dto");
const create_platform_details_dto_1 = require("./details/create-platform-details.dto");
class CreateEntityDto {
}
exports.CreateEntityDto = CreateEntityDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the entity', example: 'Awesome AI Tool' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(3),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Website URL of the entity', example: 'https://awesomeaitool.com' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "website_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the entity type (UUID)', example: 'valid-uuid-for-entity-type' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "entity_type_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Short description', example: 'A tool that revolutionizes AI development.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "short_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Full description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Logo URL', example: 'https://awesomeaitool.com/logo.png' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "logo_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Documentation URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "documentation_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contact URL or email' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "contact_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Privacy Policy URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "privacy_policy_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Year founded', example: 2021, type: Number }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1900),
    (0, class_validator_1.Max)(new Date().getFullYear()),
    __metadata("design:type", Number)
], CreateEntityDto.prototype, "founded_year", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Social media links',
        type: Object,
        example: { twitter: 'handle', linkedin: 'company/profile' }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateEntityDto.prototype, "social_links", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of Category IDs (UUIDs)',
        type: [String],
        example: ['uuid-cat1'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateEntityDto.prototype, "category_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of Tag IDs (UUIDs)',
        type: [String],
        example: ['uuid-tag1'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateEntityDto.prototype, "tag_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of Feature IDs (UUIDs) to associate with the entity.',
        type: [String],
        example: ['uuid-feat1', 'uuid-feat2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], CreateEntityDto.prototype, "feature_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Meta title for SEO', example: 'Awesome AI Tool | Best AI Solutions' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "meta_title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Meta description for SEO', example: 'Discover the Awesome AI Tool, a leader in AI development solutions.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "meta_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Employee count range', enum: prisma_1.EmployeeCountRange, example: prisma_1.EmployeeCountRange.C11_50 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.EmployeeCountRange),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "employee_count_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Funding stage', enum: prisma_1.FundingStage, example: prisma_1.FundingStage.SERIES_A }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.FundingStage),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "funding_stage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Summary of company location(s)', example: 'San Francisco, Remote' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "location_summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Affiliate referral link', example: 'https://awesomeaitool.com/?ref=partner' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "ref_link", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Affiliate status', enum: prisma_1.AffiliateStatus, example: prisma_1.AffiliateStatus.APPROVED }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.AffiliateStatus),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "affiliate_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Label for scraped review sentiment', example: 'Positive' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "scraped_review_sentiment_label", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Score for scraped review sentiment (e.g., 0.0 to 1.0)', example: 0.85, type: Number }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], CreateEntityDto.prototype, "scraped_review_sentiment_score", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Count of scraped reviews', example: 150, type: Number }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateEntityDto.prototype, "scraped_review_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the entity (e.g., PENDING, ACTIVE). Typically set by the system or admins.',
        enum: prisma_1.EntityStatus,
        example: prisma_1.EntityStatus.PENDING,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.EntityStatus),
    __metadata("design:type", String)
], CreateEntityDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Indicates if live chat support is available',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateEntityDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an AI Tool' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_tool_details_dto_1.CreateToolDetailsDto),
    __metadata("design:type", create_tool_details_dto_1.CreateToolDetailsDto)
], CreateEntityDto.prototype, "tool_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Course' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_course_details_dto_1.CreateCourseDetailsDto),
    __metadata("design:type", create_course_details_dto_1.CreateCourseDetailsDto)
], CreateEntityDto.prototype, "course_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Agency' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_agency_details_dto_1.CreateAgencyDetailsDto),
    __metadata("design:type", create_agency_details_dto_1.CreateAgencyDetailsDto)
], CreateEntityDto.prototype, "agency_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Content Creator' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_content_creator_details_dto_1.CreateContentCreatorDetailsDto),
    __metadata("design:type", create_content_creator_details_dto_1.CreateContentCreatorDetailsDto)
], CreateEntityDto.prototype, "content_creator_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Community' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_community_details_dto_1.CreateCommunityDetailsDto),
    __metadata("design:type", create_community_details_dto_1.CreateCommunityDetailsDto)
], CreateEntityDto.prototype, "community_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Newsletter' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_newsletter_details_dto_1.CreateNewsletterDetailsDto),
    __metadata("design:type", create_newsletter_details_dto_1.CreateNewsletterDetailsDto)
], CreateEntityDto.prototype, "newsletter_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Dataset' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_dataset_details_dto_1.CreateDatasetDetailsDto),
    __metadata("design:type", create_dataset_details_dto_1.CreateDatasetDetailsDto)
], CreateEntityDto.prototype, "dataset_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Research Paper' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_research_paper_details_dto_1.CreateResearchPaperDetailsDto),
    __metadata("design:type", create_research_paper_details_dto_1.CreateResearchPaperDetailsDto)
], CreateEntityDto.prototype, "research_paper_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for Software' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_software_details_dto_1.CreateSoftwareDetailsDto),
    __metadata("design:type", create_software_details_dto_1.CreateSoftwareDetailsDto)
], CreateEntityDto.prototype, "software_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Model' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_model_details_dto_1.CreateModelDetailsDto),
    __metadata("design:type", create_model_details_dto_1.CreateModelDetailsDto)
], CreateEntityDto.prototype, "model_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Project Reference' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_project_reference_details_dto_1.CreateProjectReferenceDetailsDto),
    __metadata("design:type", create_project_reference_details_dto_1.CreateProjectReferenceDetailsDto)
], CreateEntityDto.prototype, "project_reference_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Service Provider' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_service_provider_details_dto_1.CreateServiceProviderDetailsDto),
    __metadata("design:type", create_service_provider_details_dto_1.CreateServiceProviderDetailsDto)
], CreateEntityDto.prototype, "service_provider_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Investor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_investor_details_dto_1.CreateInvestorDetailsDto),
    __metadata("design:type", create_investor_details_dto_1.CreateInvestorDetailsDto)
], CreateEntityDto.prototype, "investor_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Event' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_event_details_dto_1.CreateEventDetailsDto),
    __metadata("design:type", create_event_details_dto_1.CreateEventDetailsDto)
], CreateEntityDto.prototype, "event_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Job' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_job_details_dto_1.CreateJobDetailsDto),
    __metadata("design:type", create_job_details_dto_1.CreateJobDetailsDto)
], CreateEntityDto.prototype, "job_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Grant' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_grant_details_dto_1.CreateGrantDetailsDto),
    __metadata("design:type", create_grant_details_dto_1.CreateGrantDetailsDto)
], CreateEntityDto.prototype, "grant_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Bounty' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_bounty_details_dto_1.CreateBountyDetailsDto),
    __metadata("design:type", create_bounty_details_dto_1.CreateBountyDetailsDto)
], CreateEntityDto.prototype, "bounty_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for Hardware' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_hardware_details_dto_1.CreateHardwareDetailsDto),
    __metadata("design:type", create_hardware_details_dto_1.CreateHardwareDetailsDto)
], CreateEntityDto.prototype, "hardware_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for News' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_news_details_dto_1.CreateNewsDetailsDto),
    __metadata("design:type", create_news_details_dto_1.CreateNewsDetailsDto)
], CreateEntityDto.prototype, "news_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Book' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_book_details_dto_1.CreateBookDetailsDto),
    __metadata("design:type", create_book_details_dto_1.CreateBookDetailsDto)
], CreateEntityDto.prototype, "book_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Podcast' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_podcast_details_dto_1.CreatePodcastDetailsDto),
    __metadata("design:type", create_podcast_details_dto_1.CreatePodcastDetailsDto)
], CreateEntityDto.prototype, "podcast_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Platform' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => create_platform_details_dto_1.CreatePlatformDetailsDto),
    __metadata("design:type", create_platform_details_dto_1.CreatePlatformDetailsDto)
], CreateEntityDto.prototype, "platform_details", void 0);
//# sourceMappingURL=create-entity.dto.js.map