"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const supabase_js_1 = require("@supabase/supabase-js");
function getEnvOrThrow(config, key) {
    const value = config.get(key);
    if (!value) {
        throw new Error(`${key} is missing from environment variables`);
    }
    return value;
}
let SupabaseService = class SupabaseService {
    constructor(configService) {
        this.configService = configService;
        const supabaseUrl = getEnvOrThrow(this.configService, 'SUPABASE_URL');
        const supabaseAnonKey = getEnvOrThrow(this.configService, 'SUPABASE_ANON_KEY');
        const supabaseServiceRoleKey = getEnvOrThrow(this.configService, 'SUPABASE_SERVICE_ROLE_KEY');
        const serverOptions = {
            auth: {
                autoRefreshToken: false,
                persistSession: false,
                detectSessionInUrl: false
            }
        };
        this.supabaseAnonClient = (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, serverOptions);
        this.supabaseAdminClient = (0, supabase_js_1.createClient)(supabaseUrl, supabaseServiceRoleKey, serverOptions);
    }
    getClient() {
        return this.supabaseAnonClient;
    }
    getAdminClient() {
        return this.supabaseAdminClient;
    }
};
exports.SupabaseService = SupabaseService;
exports.SupabaseService = SupabaseService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SupabaseService);
//# sourceMappingURL=supabase.service.js.map