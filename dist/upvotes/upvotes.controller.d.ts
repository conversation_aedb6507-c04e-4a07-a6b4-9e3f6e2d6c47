import { UpvotesService } from './upvotes.service';
import { User as UserModel } from 'generated/prisma';
import { AppLoggerService } from '../common/logger/logger.service';
export declare class UpvotesController {
    private readonly upvotesService;
    private readonly logger;
    constructor(upvotesService: UpvotesService, logger: AppLoggerService);
    addUpvote(user: UserModel, entityId: string): Promise<{
        entityId: string;
        userId: string;
        createdAt: Date;
    }>;
    removeUpvote(user: UserModel, entityId: string): Promise<void>;
}
