import { Modu<PERSON> } from '@nestjs/common';
import { AdminEntitiesController } from './admin-entities.controller';
import { EntitiesModule } from '../../entities/entities.module'; // Import EntitiesModule to access EntitiesService
import { SupabaseModule } from '../../supabase/supabase.module'; // Import SupabaseModule for SupabaseAuthGuard
import { PrismaModule } from '../../prisma/prisma.module'; // Import PrismaModule for SupabaseAuthGuard
// If EntitiesService is not exported from EntitiesModule or you want a more decoupled approach,
// you might need to provide EntitiesService directly or import a shared services module.

@Module({
  imports: [
    EntitiesModule, // Provides EntitiesService
    SupabaseModule, // Provides SupabaseService for SupabaseAuthGuard
    PrismaModule, // Provides PrismaService for SupabaseAuthGuard
  ],
  controllers: [AdminEntitiesController],
  // providers: [], // EntitiesService is provided by EntitiesModule
})
export class AdminEntitiesModule {} 