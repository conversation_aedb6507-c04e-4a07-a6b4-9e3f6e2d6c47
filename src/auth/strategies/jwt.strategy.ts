import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
// import { SupabaseJwtPayload } from './supabase-jwt-payload.interface'; // Removed import
import { PrismaService } from '../../prisma/prisma.service'; // Import PrismaService
import { SupabaseService } from '../../supabase/supabase.service'; // Import SupabaseService
import { User as PublicUserModel } from '../../../generated/prisma'; // Import User model from public schema

// Define SupabaseJwtPayload interface directly here
export interface SupabaseJwtPayload {
  sub: string; // User ID (from auth.users.id)
  email?: string;
  phone?: string;
  role?: string; // Often 'authenticated'
  aud?: string; // Often 'authenticated'
  exp?: number; // Expiration timestamp (seconds)
  iat?: number; // Issued at timestamp (seconds)
  app_metadata?: { [key: string]: any };
  user_metadata?: { [key: string]: any };
  // Add any other custom claims if configured in Supabase
}

// Define the new ReqUserObject interface
export interface ReqUserObject {
  authData: SupabaseJwtPayload; // The raw Supabase JWT payload
  dbProfile: PublicUserModel | null;   // The profile from public.users (or null)
  profileExistsInDb: boolean;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService, // Inject PrismaService
    private readonly supabaseService: SupabaseService, // Inject SupabaseService
  ) {
    // Use the actual Supabase JWT secret for proper token validation
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true, // We'll let Supabase handle expiration
      secretOrKey: this.configService.get<string>('SUPABASE_JWT_SECRET') || this.configService.get<string>('JWT_SECRET'),
      passReqToCallback: true, // Pass request to get the raw JWT token
    });
  }

  async validate(req: any, payload: SupabaseJwtPayload): Promise<ReqUserObject> {
    console.log('--- JWT STRATEGY VALIDATE METHOD ENTERED ---');

    try {
      // Extract the JWT token from the request
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('Missing or invalid authorization header.');
      }

      const jwtToken = authHeader.substring(7); // Remove 'Bearer ' prefix
      console.log('JWT Token extracted from header');

      // Use Supabase ADMIN client to validate the JWT token (server-side validation)
      const supabase = this.supabaseService.getAdminClient();
      const { data: { user }, error } = await supabase.auth.getUser(jwtToken);

      if (error) {
        console.error('[JwtStrategy] Supabase JWT validation error:', error);
        throw new UnauthorizedException('Invalid JWT token: ' + error.message);
      }

      if (!user) {
        console.error('[JwtStrategy] No user returned from Supabase JWT validation');
        throw new UnauthorizedException('Invalid JWT token: no user found');
      }

      console.log(`[JwtStrategy] Supabase JWT validation successful for user: ${user.id}`);

      // Find the user in our public.users table using the authUserId
      console.log(`[JwtStrategy] Attempting to find user in database...`);
      const userProfile = await this.prismaService.user.findUnique({
        where: { authUserId: user.id }, // Use the unique authUserId field
      });

      if (userProfile) {
        console.log(`[JwtStrategy] User profile found in DB for authUserId: ${user.id}, public.users ID: ${userProfile.id}`);
      } else {
        console.log(`[JwtStrategy] User profile NOT found in DB for authUserId: ${user.id}. This is okay for initial sync.`);
      }

      console.log('--- JWT STRATEGY VALIDATE METHOD COMPLETED SUCCESSFULLY ---');
      return {
        authData: user as any, // Use the validated user data from Supabase
        dbProfile: userProfile,
        profileExistsInDb: !!userProfile,
      };
    } catch (error) {
      console.error('--- JWT STRATEGY VALIDATE METHOD ERROR ---');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);
      throw error; // Re-throw to let NestJS handle it
    }
  }
} 