import { Module } from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { BookmarksController } from './bookmarks.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module'; // For SupabaseAuthGuard dependency
import { SupabaseModule } from '../supabase/supabase.module'; // For SupabaseAuthGuard dependency
import { LoggerModule } from '../common/logger/logger.module';
// import { EntitiesModule } from '../entities/entities.module'; // If EntitiesService is needed

@Module({
  imports: [
    PrismaModule,
    AuthModule, // To make SupabaseAuthGuard available and req.user populated
    SupabaseModule, // For SupabaseService dependency in SupabaseAuthGuard
    LoggerModule, // For structured logging
    // EntitiesModule, // If direct interaction with EntitiesService for validation needed
  ],
  controllers: [BookmarksController],
  providers: [BookmarksService],
})
export class BookmarksModule {} 