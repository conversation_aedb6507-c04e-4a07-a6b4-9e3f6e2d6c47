import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsUrl,
  IsUUID,
  IsArray,
  ValidateNested,
  IsEnum,
  IsInt,
  Min,
  Max,
  ArrayMinSize,
  IsObject,
  MinLength,
  MaxLength,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { EntityStatus, AffiliateStatus, TechnicalLevel, LearningCurve, PricingModel, PriceRange, SkillLevel, Prisma, EmployeeCountRange, FundingStage } from 'generated/prisma';

// Import detail DTOs from their new locations
import { CreateToolDetailsDto } from './details/create-tool-details.dto';
import { CreateCourseDetailsDto } from './details/create-course-details.dto';
import { CreateAgencyDetailsDto } from './details/create-agency-details.dto';
import { CreateContentCreatorDetailsDto } from './details/create-content-creator-details.dto';
import { CreateCommunityDetailsDto } from './details/create-community-details.dto';
import { CreateNewsletterDetailsDto } from './details/create-newsletter-details.dto';
import { CreateDatasetDetailsDto } from './details/create-dataset-details.dto';
import { CreateResearchPaperDetailsDto } from './details/create-research-paper-details.dto';
import { CreateSoftwareDetailsDto } from './details/create-software-details.dto';
import { CreateModelDetailsDto } from './details/create-model-details.dto';
import { CreateProjectReferenceDetailsDto } from './details/create-project-reference-details.dto';
import { CreateServiceProviderDetailsDto } from './details/create-service-provider-details.dto';
import { CreateInvestorDetailsDto } from './details/create-investor-details.dto';
import { CreateEventDetailsDto } from './details/create-event-details.dto';
import { CreateJobDetailsDto } from './details/create-job-details.dto';
import { CreateGrantDetailsDto } from './details/create-grant-details.dto';
import { CreateBountyDetailsDto } from './details/create-bounty-details.dto';
import { CreateHardwareDetailsDto } from './details/create-hardware-details.dto';
import { CreateNewsDetailsDto } from './details/create-news-details.dto';
import { CreateBookDetailsDto } from './details/create-book-details.dto';
import { CreatePodcastDetailsDto } from './details/create-podcast-details.dto';
import { CreatePlatformDetailsDto } from './details/create-platform-details.dto';

// Main DTO
export class CreateEntityDto {
  @ApiProperty({ description: 'Name of the entity', example: 'Awesome AI Tool' })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: 'Website URL of the entity', example: 'https://awesomeaitool.com' })
  @IsNotEmpty()
  @IsUrl()
  website_url: string;

  @ApiProperty({ description: 'ID of the entity type (UUID)', example: 'valid-uuid-for-entity-type' })
  @IsNotEmpty()
  @IsUUID('4')
  entity_type_id: string;

  @ApiPropertyOptional({ description: 'Short description', example: 'A tool that revolutionizes AI development.' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  short_description?: string;

  @ApiPropertyOptional({ description: 'Full description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Logo URL', example: 'https://awesomeaitool.com/logo.png' })
  @IsOptional()
  @IsUrl()
  logo_url?: string;

  @ApiPropertyOptional({ description: 'Documentation URL' })
  @IsOptional()
  @IsUrl()
  documentation_url?: string;

  @ApiPropertyOptional({ description: 'Contact URL or email' })
  @IsOptional()
  @IsString()
  contact_url?: string;

  @ApiPropertyOptional({ description: 'Privacy Policy URL' })
  @IsOptional()
  @IsUrl()
  privacy_policy_url?: string;

  @ApiPropertyOptional({ description: 'Year founded', example: 2021, type: Number })
  @IsOptional()
  @IsInt()
  @Min(1900)
  @Max(new Date().getFullYear())
  founded_year?: number;

  @ApiPropertyOptional({ 
    description: 'Social media links', 
    type: Object,
    example: { twitter: 'handle', linkedin: 'company/profile' }
  })
  @IsOptional()
  @IsObject()
  social_links?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Array of Category IDs (UUIDs)',
    type: [String],
    example: ['uuid-cat1'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @ArrayMinSize(1)
  category_ids?: string[];

  @ApiPropertyOptional({
    description: 'Array of Tag IDs (UUIDs)',
    type: [String],
    example: ['uuid-tag1'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @ArrayMinSize(1)
  tag_ids?: string[];

  @ApiPropertyOptional({
    description: 'Array of Feature IDs (UUIDs) to associate with the entity.',
    type: [String],
    example: ['uuid-feat1', 'uuid-feat2'],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  feature_ids?: string[];

  // New direct Entity fields
  @ApiPropertyOptional({ description: 'Meta title for SEO', example: 'Awesome AI Tool | Best AI Solutions' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  meta_title?: string;

  @ApiPropertyOptional({ description: 'Meta description for SEO', example: 'Discover the Awesome AI Tool, a leader in AI development solutions.' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  meta_description?: string;

  @ApiPropertyOptional({ description: 'Employee count range', enum: EmployeeCountRange, example: EmployeeCountRange.C11_50 })
  @IsOptional()
  @IsEnum(EmployeeCountRange)
  employee_count_range?: EmployeeCountRange;

  @ApiPropertyOptional({ description: 'Funding stage', enum: FundingStage, example: FundingStage.SERIES_A })
  @IsOptional()
  @IsEnum(FundingStage)
  funding_stage?: FundingStage;

  @ApiPropertyOptional({ description: 'Summary of company location(s)', example: 'San Francisco, Remote' })
  @IsOptional()
  @IsString()
  location_summary?: string;

  @ApiPropertyOptional({ description: 'Affiliate referral link', example: 'https://awesomeaitool.com/?ref=partner' })
  @IsOptional()
  @IsUrl()
  ref_link?: string;

  @ApiPropertyOptional({ description: 'Affiliate status', enum: AffiliateStatus, example: AffiliateStatus.APPROVED })
  @IsOptional()
  @IsEnum(AffiliateStatus)
  affiliate_status?: AffiliateStatus;

  @ApiPropertyOptional({ description: 'Label for scraped review sentiment', example: 'Positive' })
  @IsOptional()
  @IsString()
  scraped_review_sentiment_label?: string;

  @ApiPropertyOptional({ description: 'Score for scraped review sentiment (e.g., 0.0 to 1.0)', example: 0.85, type: Number })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  scraped_review_sentiment_score?: number;

  @ApiPropertyOptional({ description: 'Count of scraped reviews', example: 150, type: Number })
  @IsOptional()
  @IsInt()
  @Min(0)
  scraped_review_count?: number;

  @ApiPropertyOptional({ 
    description: 'Status of the entity (e.g., PENDING, ACTIVE). Typically set by the system or admins.',
    enum: EntityStatus,
    example: EntityStatus.PENDING,
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  has_live_chat?: boolean;

  // Type-Specific Details (imports are now from ./details/)
  @ApiPropertyOptional({ description: 'Details for an AI Tool' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateToolDetailsDto)
  tool_details?: CreateToolDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Course' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateCourseDetailsDto)
  course_details?: CreateCourseDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Agency' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateAgencyDetailsDto)
  agency_details?: CreateAgencyDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Content Creator' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateContentCreatorDetailsDto)
  content_creator_details?: CreateContentCreatorDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Community' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateCommunityDetailsDto)
  community_details?: CreateCommunityDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Newsletter' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateNewsletterDetailsDto)
  newsletter_details?: CreateNewsletterDetailsDto;

  // Add missing nested detail DTOs
  @ApiPropertyOptional({ description: 'Details for a Dataset' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateDatasetDetailsDto)
  dataset_details?: CreateDatasetDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Research Paper' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateResearchPaperDetailsDto)
  research_paper_details?: CreateResearchPaperDetailsDto;

  @ApiPropertyOptional({ description: 'Details for Software' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateSoftwareDetailsDto)
  software_details?: CreateSoftwareDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Model' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateModelDetailsDto)
  model_details?: CreateModelDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Project Reference' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateProjectReferenceDetailsDto)
  project_reference_details?: CreateProjectReferenceDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Service Provider' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateServiceProviderDetailsDto)
  service_provider_details?: CreateServiceProviderDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Investor' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateInvestorDetailsDto)
  investor_details?: CreateInvestorDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Event' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateEventDetailsDto)
  event_details?: CreateEventDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Job' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateJobDetailsDto)
  job_details?: CreateJobDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Grant' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateGrantDetailsDto)
  grant_details?: CreateGrantDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Bounty' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateBountyDetailsDto)
  bounty_details?: CreateBountyDetailsDto;

  @ApiPropertyOptional({ description: 'Details for Hardware' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateHardwareDetailsDto)
  hardware_details?: CreateHardwareDetailsDto;

  @ApiPropertyOptional({ description: 'Details for News' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateNewsDetailsDto)
  news_details?: CreateNewsDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Book' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateBookDetailsDto)
  book_details?: CreateBookDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Podcast' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreatePodcastDetailsDto)
  podcast_details?: CreatePodcastDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Platform' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreatePlatformDetailsDto)
  platform_details?: CreatePlatformDetailsDto;
} 