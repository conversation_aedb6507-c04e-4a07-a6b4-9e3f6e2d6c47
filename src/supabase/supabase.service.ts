import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';


function getEnvOrThrow(config: ConfigService, key: string): string {
  const value = config.get<string>(key);
  if (!value) {
    throw new Error(`${key} is missing from environment variables`);
  }
  return value;
}

@Injectable()
export class SupabaseService {
  private supabaseAnonClient: SupabaseClient;
  private supabaseAdminClient: SupabaseClient;

  constructor(private configService: ConfigService) {
    const supabaseUrl = getEnvOrThrow(this.configService, 'SUPABASE_URL');
    const supabaseAnonKey = getEnvOrThrow(this.configService, 'SUPABASE_ANON_KEY');
    const supabaseServiceRoleKey = getEnvOrThrow(this.configService, 'SUPABASE_SERVICE_ROLE_KEY');

    // Configure clients for server-side usage
    const serverOptions = {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
      }
    };

    this.supabaseAnonClient = createClient(supabaseUrl, supabaseAnonKey, serverOptions);
    this.supabaseAdminClient = createClient(supabaseUrl, supabaseServiceRoleKey, serverOptions);
  }

  getClient(): SupabaseClient {
    return this.supabaseAnonClient;
  }

  getAdminClient(): SupabaseClient {
    return this.supabaseAdminClient;
  }
}